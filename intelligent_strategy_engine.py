"""
Intelligent Strategy Recommendation Engine
The "Brain" of <PERSON>'s Trading System

This is the master engine that combines all analysis components to think like <PERSON>
when selecting optimal strategies. It integrates:

1. Multi-factor market analysis
2. Stock-specific factor analysis  
3. Intelligent decision tree logic
4. <PERSON>'s specific criteria and preferences
5. Real-time market regime detection
6. Confidence scoring and risk assessment

Output: Single "Strategy of the Day" recommendation per stock with detailed reasoning
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime
import logging

from market_analysis_engine import MarketAnalysisEngine, MarketFactors, StockSpecificFactors
from strategy_decision_tree import StrategyDecisionTree, StrategyRecommendation, StrategyConfidence
from daily_outline import TradingSignal, StrategyType, MarketCondition
from enhanced_strategies import STOCK_RULES

@dataclass
class StrategyOfTheDay:
    """Complete 'Strategy of the Day' recommendation"""
    symbol: str
    date: datetime
    
    # Primary recommendation
    recommended_strategy: StrategyType
    confidence: float
    confidence_level: StrategyConfidence
    
    # Detailed analysis
    market_environment_summary: str
    stock_analysis_summary: str
    erica_criteria_alignment: str
    
    # Execution guidance
    specific_trade_setup: str
    entry_criteria: List[str]
    exit_criteria: List[str]
    position_sizing_guidance: str
    
    # Risk management
    key_risks: List[str]
    risk_mitigation: List[str]
    stop_loss_guidance: str
    
    # Market monitoring
    conditions_to_watch: List[str]
    invalidation_signals: List[str]
    upgrade_opportunities: List[str]
    
    # Alternative strategies
    backup_strategies: List[Tuple[StrategyType, float, str]]  # (strategy, confidence, reason)
    
    # Factor scores (for transparency)
    factor_breakdown: Dict[str, float]

@dataclass
class MarketEnvironmentReport:
    """Daily market environment assessment"""
    date: datetime
    
    # Overall market assessment
    market_regime: str
    volatility_environment: str
    sentiment_reading: str
    
    # Key market factors
    vix_analysis: str
    put_call_analysis: str
    breadth_analysis: str
    sector_rotation_status: str
    
    # Strategy implications
    favored_strategies: List[StrategyType]
    strategies_to_avoid: List[StrategyType]
    
    # Today's focus
    key_themes: List[str]
    risk_factors: List[str]
    opportunities: List[str]

class IntelligentStrategyEngine:
    """Master strategy recommendation engine"""
    
    def __init__(self, api_key: str):
        self.market_analyzer = MarketAnalysisEngine(api_key)
        self.decision_tree = StrategyDecisionTree()
        self.logger = logging.getLogger(__name__)
        
        # Cache for market analysis (refresh every 15 minutes)
        self.market_factors_cache = None
        self.cache_timestamp = None
        
    def generate_daily_recommendations(self, symbols: List[str]) -> Tuple[MarketEnvironmentReport, List[StrategyOfTheDay]]:
        """
        Generate complete daily strategy recommendations
        
        Returns:
            - Market environment report
            - Strategy of the day for each symbol
        """
        
        # Get comprehensive market analysis
        market_factors = self._get_market_factors()
        
        # Generate market environment report
        market_report = self._create_market_environment_report(market_factors)
        
        # Generate strategy recommendations for each symbol
        daily_strategies = []
        
        for symbol in symbols:
            try:
                # Get stock-specific factors
                stock_factors = self.market_analyzer.analyze_stock_factors(symbol)
                
                # Get strategy recommendation from decision tree
                strategy_rec = self.decision_tree.recommend_strategy(symbol, market_factors, stock_factors)
                
                # Create comprehensive "Strategy of the Day"
                strategy_of_day = self._create_strategy_of_the_day(
                    symbol, market_factors, stock_factors, strategy_rec
                )
                
                daily_strategies.append(strategy_of_day)
                
            except Exception as e:
                self.logger.error(f"Error generating recommendation for {symbol}: {e}")
                continue
        
        return market_report, daily_strategies
    
    def _get_market_factors(self) -> MarketFactors:
        """Get market factors with caching"""
        now = datetime.now()
        
        # Check if cache is still valid (15 minutes)
        if (self.market_factors_cache and self.cache_timestamp and 
            (now - self.cache_timestamp).seconds < 900):
            return self.market_factors_cache
        
        # Refresh market analysis
        self.market_factors_cache = self.market_analyzer.analyze_market_factors()
        self.cache_timestamp = now
        
        return self.market_factors_cache
    
    def _create_market_environment_report(self, market_factors: MarketFactors) -> MarketEnvironmentReport:
        """Create comprehensive market environment report"""
        
        # Market regime analysis
        regime_desc = {
            "bull_market": "Bull Market - Sustained uptrend with strong momentum",
            "bear_market": "Bear Market - Sustained downtrend with selling pressure", 
            "sideways_market": "Sideways Market - Range-bound with no clear direction",
            "transition": "Market Transition - Regime change in progress"
        }.get(market_factors.market_regime.value, "Unknown")
        
        # Volatility environment
        vol_desc = {
            "low_volatility": f"Low Volatility Environment (VIX: {market_factors.vix_level:.1f})",
            "normal_volatility": f"Normal Volatility Environment (VIX: {market_factors.vix_level:.1f})",
            "high_volatility": f"High Volatility Environment (VIX: {market_factors.vix_level:.1f})",
            "extreme_volatility": f"Extreme Volatility Environment (VIX: {market_factors.vix_level:.1f})"
        }.get(market_factors.volatility_regime.value, "Unknown")
        
        # Sentiment reading
        sentiment_desc = {
            "extreme_fear": f"Extreme Fear (Fear/Greed: {market_factors.fear_greed_index:.0f})",
            "fear": f"Fearful Sentiment (Fear/Greed: {market_factors.fear_greed_index:.0f})",
            "neutral": f"Neutral Sentiment (Fear/Greed: {market_factors.fear_greed_index:.0f})",
            "greed": f"Greedy Sentiment (Fear/Greed: {market_factors.fear_greed_index:.0f})",
            "extreme_greed": f"Extreme Greed (Fear/Greed: {market_factors.fear_greed_index:.0f})"
        }.get(market_factors.sentiment_level.value, "Unknown")
        
        # Determine favored strategies based on environment
        favored_strategies = self._determine_favored_strategies(market_factors)
        strategies_to_avoid = self._determine_strategies_to_avoid(market_factors)
        
        # Generate key themes
        key_themes = self._generate_market_themes(market_factors)
        risk_factors = self._identify_market_risks(market_factors)
        opportunities = self._identify_market_opportunities(market_factors)
        
        return MarketEnvironmentReport(
            date=datetime.now(),
            market_regime=regime_desc,
            volatility_environment=vol_desc,
            sentiment_reading=sentiment_desc,
            vix_analysis=f"VIX at {market_factors.vix_level:.1f} ({market_factors.vix_percentile:.0f}th percentile)",
            put_call_analysis=f"Put/Call ratio: {market_factors.put_call_ratio:.2f}",
            breadth_analysis=f"Market breadth: {market_factors.market_breadth:.2f} (A/D ratio)",
            sector_rotation_status=market_factors.sector_rotation_signal,
            favored_strategies=favored_strategies,
            strategies_to_avoid=strategies_to_avoid,
            key_themes=key_themes,
            risk_factors=risk_factors,
            opportunities=opportunities
        )
    
    def _create_strategy_of_the_day(self, symbol: str, market_factors: MarketFactors,
                                  stock_factors: StockSpecificFactors,
                                  strategy_rec: StrategyRecommendation) -> StrategyOfTheDay:
        """Create comprehensive Strategy of the Day recommendation"""
        
        # Market environment summary
        market_summary = self._create_market_summary_for_stock(symbol, market_factors)
        
        # Stock analysis summary
        stock_summary = self._create_stock_analysis_summary(symbol, stock_factors)
        
        # Erica criteria alignment
        erica_alignment = self._explain_erica_criteria_alignment(
            strategy_rec.primary_strategy, symbol, market_factors, stock_factors
        )
        
        # Specific trade setup
        trade_setup = self._generate_specific_trade_setup(
            symbol, strategy_rec.primary_strategy, strategy_rec, stock_factors
        )
        
        # Entry and exit criteria
        entry_criteria = self._generate_entry_criteria(strategy_rec.primary_strategy, symbol, market_factors, stock_factors)
        exit_criteria = self._generate_exit_criteria(strategy_rec.primary_strategy, symbol)
        
        # Position sizing guidance
        position_guidance = self._generate_position_sizing_guidance(
            symbol, strategy_rec.primary_strategy, strategy_rec.position_size_multiplier, market_factors
        )
        
        # Risk management
        risk_mitigation = self._generate_risk_mitigation_strategies(
            strategy_rec.primary_strategy, symbol, market_factors, stock_factors
        )
        stop_loss_guidance = self._generate_stop_loss_guidance(strategy_rec.primary_strategy, symbol)
        
        # Monitoring guidance
        conditions_to_watch = self._generate_monitoring_conditions(
            strategy_rec.primary_strategy, symbol, market_factors, stock_factors
        )
        
        # Backup strategies with explanations
        backup_strategies = []
        for alt_strategy, alt_confidence in strategy_rec.alternative_strategies:
            reason = self._explain_alternative_strategy(alt_strategy, symbol, market_factors, stock_factors)
            backup_strategies.append((alt_strategy, alt_confidence, reason))
        
        return StrategyOfTheDay(
            symbol=symbol,
            date=datetime.now(),
            recommended_strategy=strategy_rec.primary_strategy,
            confidence=strategy_rec.confidence,
            confidence_level=strategy_rec.confidence_level,
            market_environment_summary=market_summary,
            stock_analysis_summary=stock_summary,
            erica_criteria_alignment=erica_alignment,
            specific_trade_setup=trade_setup,
            entry_criteria=entry_criteria,
            exit_criteria=exit_criteria,
            position_sizing_guidance=position_guidance,
            key_risks=strategy_rec.key_risk_factors,
            risk_mitigation=risk_mitigation,
            stop_loss_guidance=stop_loss_guidance,
            conditions_to_watch=conditions_to_watch,
            invalidation_signals=strategy_rec.invalidation_triggers,
            upgrade_opportunities=strategy_rec.upgrade_triggers,
            backup_strategies=backup_strategies,
            factor_breakdown={
                'market_environment': strategy_rec.market_environment_score,
                'stock_specific': strategy_rec.stock_specific_score,
                'erica_criteria': strategy_rec.erica_criteria_score,
                'risk_adjusted': strategy_rec.risk_adjusted_score
            }
        )
    
    def _determine_favored_strategies(self, market_factors: MarketFactors) -> List[StrategyType]:
        """Determine which strategies are favored in current environment"""
        favored = []
        
        # High volatility favors premium selling
        if market_factors.volatility_factors > 0.7:
            favored.extend([StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING])
        
        # Bull market favors directional strategies
        if market_factors.bullish_factors > 0.7:
            favored.extend([StrategyType.LEAPS, StrategyType.CREDIT_SPREAD])
        
        # Sideways market favors income strategies
        if market_factors.market_regime.value == "sideways_market":
            favored.extend([StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING])
        
        return list(set(favored))  # Remove duplicates
    
    def _determine_strategies_to_avoid(self, market_factors: MarketFactors) -> List[StrategyType]:
        """Determine which strategies to avoid in current environment"""
        avoid = []
        
        # Bear market - avoid bullish strategies
        if market_factors.bearish_factors > 0.7:
            avoid.extend([StrategyType.LEAPS, StrategyType.CREDIT_SPREAD])
        
        # Low volatility - avoid premium selling
        if market_factors.volatility_factors < 0.3:
            avoid.extend([StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING])
        
        # High uncertainty - avoid long-term strategies
        if market_factors.uncertainty_factors > 0.7:
            avoid.append(StrategyType.LEAPS)
        
        return list(set(avoid))
    
    def _generate_market_themes(self, market_factors: MarketFactors) -> List[str]:
        """Generate key market themes for the day"""
        themes = []
        
        if market_factors.volatility_factors > 0.7:
            themes.append("High volatility environment - premium selling opportunities")
        
        if market_factors.earnings_season_intensity > 0.6:
            themes.append("Active earnings season - increased volatility expected")
        
        if market_factors.fed_meeting_proximity <= 7:
            themes.append("Fed meeting approaching - policy uncertainty")
        
        if market_factors.sector_rotation_signal != "neutral":
            themes.append(f"Sector rotation in progress - {market_factors.sector_rotation_signal}")
        
        return themes
    
    def _identify_market_risks(self, market_factors: MarketFactors) -> List[str]:
        """Identify key market risks"""
        risks = []
        
        if market_factors.uncertainty_factors > 0.6:
            risks.append("Elevated market uncertainty")
        
        if market_factors.volatility_regime.value == "extreme_volatility":
            risks.append("Extreme volatility environment")
        
        if market_factors.sentiment_level.value in ["extreme_fear", "extreme_greed"]:
            risks.append("Extreme sentiment readings - potential reversal risk")
        
        if market_factors.major_events_this_week:
            risks.append(f"Major events this week: {', '.join(market_factors.major_events_this_week)}")
        
        return risks
    
    def _identify_market_opportunities(self, market_factors: MarketFactors) -> List[str]:
        """Identify key market opportunities"""
        opportunities = []
        
        if market_factors.volatility_factors > 0.7:
            opportunities.append("High IV environment - excellent for premium selling")
        
        if market_factors.sentiment_level.value == "extreme_fear":
            opportunities.append("Extreme fear - potential contrarian opportunities")
        
        if market_factors.bullish_factors > 0.7:
            opportunities.append("Strong bullish setup - directional strategies favored")
        
        return opportunities
    
    def _create_market_summary_for_stock(self, symbol: str, market_factors: MarketFactors) -> str:
        """Create market environment summary specific to this stock"""
        summary_parts = []
        
        summary_parts.append(f"Market regime: {market_factors.market_regime.value}")
        summary_parts.append(f"Volatility: {market_factors.volatility_regime.value}")
        summary_parts.append(f"Sentiment: {market_factors.sentiment_level.value}")
        
        if symbol in STOCK_RULES:
            rules = STOCK_RULES[symbol]
            if market_factors.volatility_factors > 0.7:
                summary_parts.append(f"High volatility favors {symbol}'s premium selling strategies")
        
        return ". ".join(summary_parts)
    
    def _create_stock_analysis_summary(self, symbol: str, stock_factors: StockSpecificFactors) -> str:
        """Create stock-specific analysis summary"""
        summary_parts = []
        
        # Technical analysis
        if stock_factors.technical_confluence_score > 0.7:
            summary_parts.append("Strong technical setup")
        elif stock_factors.technical_confluence_score < 0.3:
            summary_parts.append("Weak technical setup")
        else:
            summary_parts.append("Mixed technical signals")
        
        # IV analysis
        if stock_factors.iv_rank > 0.7:
            summary_parts.append(f"High IV rank ({stock_factors.iv_rank:.0%}) - premium selling favored")
        elif stock_factors.iv_rank < 0.3:
            summary_parts.append(f"Low IV rank ({stock_factors.iv_rank:.0%}) - option buying favored")
        
        # Earnings proximity
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 14:
            summary_parts.append(f"Earnings in {stock_factors.earnings_days_away} days")
        
        # News sentiment
        if stock_factors.news_sentiment_score > 0.3:
            summary_parts.append("Positive news sentiment")
        elif stock_factors.news_sentiment_score < -0.3:
            summary_parts.append("Negative news sentiment")
        
        return ". ".join(summary_parts)
    
    def _explain_erica_criteria_alignment(self, strategy: StrategyType, symbol: str,
                                        market_factors: MarketFactors,
                                        stock_factors: StockSpecificFactors) -> str:
        """Explain how recommendation aligns with Erica's criteria"""
        
        explanations = []
        
        if strategy == StrategyType.COVERED_CALL:
            explanations.append("Erica's CC criteria: High IV for premium collection")
            if stock_factors.iv_rank > 0.6:
                explanations.append(f"✓ IV rank {stock_factors.iv_rank:.0%} meets minimum threshold")
            
            if symbol in STOCK_RULES:
                rules = STOCK_RULES[symbol]
                explanations.append(f"✓ {symbol}-specific rules: {rules.cc_frequency}")
        
        elif strategy == StrategyType.CREDIT_SPREAD:
            explanations.append("Erica's CS criteria: Bullish bias with high IV")
            if market_factors.bullish_factors > 0.6:
                explanations.append("✓ Market shows bullish characteristics")
            if stock_factors.iv_rank > 0.6:
                explanations.append("✓ High IV supports premium collection")
        
        elif strategy == StrategyType.LEAPS:
            explanations.append("Erica's LEAPS criteria: Strong bullish outlook, 12+ months")
            if market_factors.market_regime.value == "bull_market":
                explanations.append("✓ Bull market supports long-term bullish strategy")
        
        elif strategy == StrategyType.PREMIUM_SELLING:
            explanations.append("Erica's premium selling: High IV rank, systematic approach")
            if market_factors.volatility_factors > 0.7:
                explanations.append("✓ High volatility environment ideal for premium selling")
        
        return ". ".join(explanations)
    
    def _generate_specific_trade_setup(self, symbol: str, strategy: StrategyType,
                                     strategy_rec: StrategyRecommendation,
                                     stock_factors: StockSpecificFactors) -> str:
        """Generate specific trade setup instructions"""
        
        dte_min, dte_max = strategy_rec.recommended_dte
        delta_min, delta_max = strategy_rec.recommended_delta
        
        if strategy == StrategyType.COVERED_CALL:
            return (f"Sell {symbol} call options {dte_min}-{dte_max} DTE, "
                   f"delta {delta_min:.2f}-{delta_max:.2f}, targeting strikes that collect "
                   f"minimum $0.30 premium per contract")
        
        elif strategy == StrategyType.CREDIT_SPREAD:
            return (f"Sell {symbol} put credit spread {dte_min}-{dte_max} DTE, "
                   f"short put delta ~{delta_max:.2f}, 5-10 point width, "
                   f"targeting 1:3 risk/reward ratio")
        
        elif strategy == StrategyType.LEAPS:
            return (f"Buy {symbol} LEAPS call options 12+ months expiration, "
                   f"delta {delta_min:.2f}-{delta_max:.2f}, consider financing "
                   f"with monthly covered calls")
        
        elif strategy == StrategyType.PREMIUM_SELLING:
            if hasattr(stock_factors, 'wheel_suitability_score') and stock_factors.wheel_suitability_score > 0.6:
                return (f"Implement {symbol} wheel strategy: sell cash-secured puts "
                       f"at support levels, transition to covered calls if assigned")
            else:
                return (f"Sell {symbol} premium systematically: cash-secured puts "
                       f"or covered calls based on position")
        
        return f"Execute {strategy.value} strategy on {symbol}"
    
    def _generate_entry_criteria(self, strategy: StrategyType, symbol: str,
                               market_factors: MarketFactors,
                               stock_factors: StockSpecificFactors) -> List[str]:
        """Generate specific entry criteria"""
        criteria = []
        
        criteria.append("Confirm option liquidity (bid-ask spread < 10% of mid)")
        criteria.append("Verify position sizing fits risk parameters")
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            criteria.append("IV rank > 50th percentile preferred")
            criteria.append("No major news/earnings within 7 days (unless specifically targeting)")
        
        if strategy == StrategyType.CREDIT_SPREAD:
            criteria.append("Confirm bullish technical setup")
            criteria.append("Support level clearly defined")
        
        if strategy == StrategyType.LEAPS:
            criteria.append("Strong uptrend confirmed")
            criteria.append("IV rank < 70th percentile for better entry")
        
        return criteria
    
    def _generate_exit_criteria(self, strategy: StrategyType, symbol: str) -> List[str]:
        """Generate exit criteria based on Erica's management rules"""
        criteria = []
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            criteria.append("Take profits at 50% of maximum profit")
            criteria.append("Close position at 21 DTE if not profitable")
            criteria.append("Roll position if still bullish and profitable")
        
        if strategy == StrategyType.LEAPS:
            criteria.append("Take profits at 100% gain or major resistance")
            criteria.append("Hold for 6-12 months unless thesis changes")
            criteria.append("Consider partial profit-taking at 50% gain")
        
        criteria.append("Exit immediately if thesis invalidated")
        
        return criteria
    
    def _generate_position_sizing_guidance(self, symbol: str, strategy: StrategyType,
                                         multiplier: float, market_factors: MarketFactors) -> str:
        """Generate position sizing guidance"""
        
        base_guidance = f"Use {multiplier:.1f}x normal position size based on current conditions"
        
        if multiplier < 1.0:
            reason = "Reduced size due to "
            factors = []
            if market_factors.uncertainty_factors > 0.6:
                factors.append("market uncertainty")
            if market_factors.volatility_regime.value == "extreme_volatility":
                factors.append("extreme volatility")
            reason += " and ".join(factors)
            return f"{base_guidance}. {reason}."
        
        elif multiplier > 1.0:
            return f"{base_guidance}. Increased size due to high-confidence setup."
        
        return f"{base_guidance}. Standard sizing appropriate."
    
    def _generate_risk_mitigation_strategies(self, strategy: StrategyType, symbol: str,
                                           market_factors: MarketFactors,
                                           stock_factors: StockSpecificFactors) -> List[str]:
        """Generate risk mitigation strategies"""
        mitigation = []
        
        if symbol == "NVDA":
            mitigation.append("Monitor for gap risk - NVDA prone to large overnight moves")
        
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 14:
            mitigation.append("Consider closing before earnings if not specifically targeting IV crush")
        
        if market_factors.uncertainty_factors > 0.6:
            mitigation.append("Reduce position sizes and take profits earlier")
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            mitigation.append("Monitor for unusual options activity that might signal large moves")
        
        mitigation.append("Set alerts for key technical levels")
        
        return mitigation
    
    def _generate_stop_loss_guidance(self, strategy: StrategyType, symbol: str) -> str:
        """Generate stop loss guidance"""
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.CREDIT_SPREAD, StrategyType.PREMIUM_SELLING]:
            return "Stop loss: Close position if loss reaches 2x premium collected"
        
        elif strategy == StrategyType.LEAPS:
            return "Stop loss: Close position if loss reaches 50% of premium paid"
        
        return "Use standard 2% account risk stop loss"
    
    def _generate_monitoring_conditions(self, strategy: StrategyType, symbol: str,
                                      market_factors: MarketFactors,
                                      stock_factors: StockSpecificFactors) -> List[str]:
        """Generate conditions to monitor during the trade"""
        conditions = []
        
        conditions.append("Monitor VIX for volatility regime changes")
        conditions.append(f"Watch {symbol} technical levels and volume")
        
        if stock_factors.earnings_days_away and stock_factors.earnings_days_away <= 30:
            conditions.append(f"Track earnings expectations and IV changes")
        
        if strategy in [StrategyType.COVERED_CALL, StrategyType.PREMIUM_SELLING]:
            conditions.append("Monitor for unusual options flow")
        
        conditions.append("Watch sector rotation and relative strength")
        
        return conditions
    
    def _explain_alternative_strategy(self, strategy: StrategyType, symbol: str,
                                    market_factors: MarketFactors,
                                    stock_factors: StockSpecificFactors) -> str:
        """Explain why alternative strategy is viable"""
        
        if strategy == StrategyType.COVERED_CALL:
            return "Alternative if IV increases or market turns sideways"
        elif strategy == StrategyType.CREDIT_SPREAD:
            return "Alternative if bullish bias strengthens"
        elif strategy == StrategyType.LEAPS:
            return "Alternative if strong uptrend develops"
        elif strategy == StrategyType.PREMIUM_SELLING:
            return "Alternative if volatility increases significantly"
        
        return "Viable alternative under different market conditions"

    def format_daily_report(self, market_report: MarketEnvironmentReport,
                           daily_strategies: List[StrategyOfTheDay]) -> str:
        """Format complete daily report with enhanced analysis"""

        lines = []
        lines.append("=" * 80)
        lines.append("🧠 INTELLIGENT STRATEGY RECOMMENDATIONS - ERICA'S FRAMEWORK")
        lines.append(f"📅 {market_report.date.strftime('%A, %B %d, %Y')}")
        lines.append("=" * 80)
        lines.append("")

        # Market Environment Section
        lines.append("🌍 MARKET ENVIRONMENT ANALYSIS")
        lines.append("-" * 40)
        lines.append(f"📊 {market_report.market_regime}")
        lines.append(f"📈 {market_report.volatility_environment}")
        lines.append(f"🎭 {market_report.sentiment_reading}")
        lines.append("")
        lines.append(f"🔍 Key Indicators:")
        lines.append(f"  • {market_report.vix_analysis}")
        lines.append(f"  • {market_report.put_call_analysis}")
        lines.append(f"  • {market_report.breadth_analysis}")
        lines.append(f"  • Sector rotation: {market_report.sector_rotation_status}")
        lines.append("")

        # Today's Focus
        if market_report.key_themes:
            lines.append("🎯 Today's Key Themes:")
            for theme in market_report.key_themes:
                lines.append(f"  • {theme}")
            lines.append("")

        # Strategy Environment
        if market_report.favored_strategies:
            strategy_names = [s.value.replace('_', ' ').title() for s in market_report.favored_strategies]
            lines.append(f"✅ Favored Strategies: {', '.join(strategy_names)}")

        if market_report.strategies_to_avoid:
            avoid_names = [s.value.replace('_', ' ').title() for s in market_report.strategies_to_avoid]
            lines.append(f"❌ Strategies to Avoid: {', '.join(avoid_names)}")
        lines.append("")

        # Risk Factors and Opportunities
        if market_report.risk_factors:
            lines.append("⚠️  Key Risk Factors:")
            for risk in market_report.risk_factors:
                lines.append(f"  • {risk}")
            lines.append("")

        if market_report.opportunities:
            lines.append("💡 Market Opportunities:")
            for opp in market_report.opportunities:
                lines.append(f"  • {opp}")
            lines.append("")

        lines.append("=" * 80)
        lines.append("")

        # Individual Stock Strategies
        for strategy in daily_strategies:
            lines.extend(self._format_strategy_of_the_day(strategy))
            lines.append("")

        # Summary
        lines.append("=" * 80)
        lines.append("📋 DAILY SUMMARY")
        lines.append("-" * 20)

        # Count strategies by type
        strategy_counts = {}
        high_confidence_count = 0

        for strategy in daily_strategies:
            strategy_type = strategy.recommended_strategy.value
            strategy_counts[strategy_type] = strategy_counts.get(strategy_type, 0) + 1

            if strategy.confidence >= 0.7:
                high_confidence_count += 1

        lines.append(f"📊 Strategy Distribution:")
        for strategy_type, count in strategy_counts.items():
            lines.append(f"  • {strategy_type.replace('_', ' ').title()}: {count}")

        lines.append(f"🎯 High Confidence Recommendations: {high_confidence_count}/{len(daily_strategies)}")
        lines.append("")
        lines.append("💡 Remember Erica's Key Principles:")
        lines.append("  • Take profits at 50% of maximum gain")
        lines.append("  • Close positions at 21 DTE if not profitable")
        lines.append("  • Never risk more than 2% of account per trade")
        lines.append("  • Systematic approach beats emotional decisions")
        lines.append("")
        lines.append("=" * 80)

        return "\n".join(lines)

    def _format_strategy_of_the_day(self, strategy: StrategyOfTheDay) -> List[str]:
        """Format individual Strategy of the Day recommendation"""

        lines = []

        # Header
        confidence_emoji = {
            "very_high": "🟢",
            "high": "🔵",
            "moderate": "🟡",
            "low": "🟠",
            "very_low": "🔴"
        }.get(strategy.confidence_level.value, "⚪")

        lines.append(f"📈 {strategy.symbol} - STRATEGY OF THE DAY")
        lines.append("-" * 50)
        lines.append(f"{confidence_emoji} **{strategy.recommended_strategy.value.replace('_', ' ').upper()}** "
                    f"(Confidence: {strategy.confidence:.0%} - {strategy.confidence_level.value.replace('_', ' ').title()})")
        lines.append("")

        # Market and Stock Analysis
        lines.append("🔍 Analysis Summary:")
        lines.append(f"  📊 Market: {strategy.market_environment_summary}")
        lines.append(f"  📈 Stock: {strategy.stock_analysis_summary}")
        lines.append(f"  ✅ Erica's Criteria: {strategy.erica_criteria_alignment}")
        lines.append("")

        # Specific Trade Setup
        lines.append("🎯 Specific Trade Setup:")
        lines.append(f"  {strategy.specific_trade_setup}")
        lines.append("")

        # Entry Criteria
        if strategy.entry_criteria:
            lines.append("📥 Entry Criteria:")
            for criteria in strategy.entry_criteria:
                lines.append(f"  ✓ {criteria}")
            lines.append("")

        # Position Sizing
        lines.append("💰 Position Sizing:")
        lines.append(f"  {strategy.position_sizing_guidance}")
        lines.append("")

        # Risk Management
        if strategy.key_risks:
            lines.append("⚠️  Key Risks:")
            for risk in strategy.key_risks:
                lines.append(f"  • {risk}")
            lines.append("")

        if strategy.risk_mitigation:
            lines.append("🛡️ Risk Mitigation:")
            for mitigation in strategy.risk_mitigation:
                lines.append(f"  • {mitigation}")
            lines.append("")

        # Exit Strategy
        if strategy.exit_criteria:
            lines.append("📤 Exit Strategy:")
            for exit_rule in strategy.exit_criteria:
                lines.append(f"  • {exit_rule}")
            lines.append("")

        # Monitoring
        if strategy.conditions_to_watch:
            lines.append("👀 Conditions to Monitor:")
            for condition in strategy.conditions_to_watch:
                lines.append(f"  • {condition}")
            lines.append("")

        # Alternative Strategies
        if strategy.backup_strategies:
            lines.append("🔄 Alternative Strategies:")
            for alt_strategy, alt_confidence, reason in strategy.backup_strategies:
                lines.append(f"  • {alt_strategy.value.replace('_', ' ').title()} "
                           f"({alt_confidence:.0%}): {reason}")
            lines.append("")

        # Factor Breakdown (for transparency)
        lines.append("📊 Decision Factor Breakdown:")
        lines.append(f"  • Market Environment: {strategy.factor_breakdown['market_environment']:.0%}")
        lines.append(f"  • Stock-Specific: {strategy.factor_breakdown['stock_specific']:.0%}")
        lines.append(f"  • Erica's Criteria: {strategy.factor_breakdown['erica_criteria']:.0%}")
        lines.append(f"  • Risk-Adjusted: {strategy.factor_breakdown['risk_adjusted']:.0%}")

        lines.append("-" * 50)

        return lines
