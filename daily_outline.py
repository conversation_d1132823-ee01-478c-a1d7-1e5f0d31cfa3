"""
AI-Powered Daily Stock Investment Planning System
Enhanced daily outline for AMD, NVDA, GOOGL, AAPL, AMZN using Financial Modeling Prep (FMP)
Incorporates <PERSON>'s trading strategies from @AbundantlyErica YouTube channel

FEATURES
--------
- Real-time market data and analysis
- <PERSON>'s trading strategies: Covered Calls, Credit Spreads, LEAPS, Premium Selling
- Technical analysis with RSI, moving averages, volatility
- Risk management and position sizing
- Daily actionable investment recommendations
- Market timing and volatility analysis

USAGE
-----
python daily_outline.py --symbols AMD,NVDA,GOOGL,AAPL,AMZN --tz America/New_York
python daily_outline.py --fmp-key YOUR_FMP_KEY --analysis-mode full
python daily_outline.py --unit-test
python daily_outline.py --self-test --fmp-key YOUR_FMP_KEY
"""

from __future__ import annotations
import os, sys, time, math, argparse, logging, textwrap
from datetime import datetime, timedelta, timezone
from dateutil import tz
import requests
from typing import Dict, List, Optional
from dataclasses import dataclass
from enum import Enum

# API Configuration
FMP_BASE = "https://financialmodelingprep.com/api/v3"
FMP_V4 = "https://financialmodelingprep.com/api/v4"

# Embedded fallback key (last resort only). Prefer env/CLI/.env/file.
FMP_FALLBACK_KEY = "K63wnAbUDHNPICjzeq3JcEG1vi8Q2oz7"

SYMBOLS_DEFAULT = ["AMD", "NVDA", "GOOGL", "AAPL", "AMZN"]

# Strategy Configuration
class StrategyType(Enum):
    COVERED_CALL = "covered_call"
    CREDIT_SPREAD = "credit_spread"
    LEAPS = "leaps"
    PREMIUM_SELLING = "premium_selling"

class MarketCondition(Enum):
    BULLISH = "bullish"
    BEARISH = "bearish"
    NEUTRAL = "neutral"
    HIGH_VOLATILITY = "high_vol"
    LOW_VOLATILITY = "low_vol"

@dataclass
class TradingSignal:
    symbol: str
    strategy: StrategyType
    action: str  # BUY, SELL, HOLD
    confidence: float  # 0-1
    reasoning: str
    entry_price: Optional[float] = None
    target_price: Optional[float] = None
    stop_loss: Optional[float] = None
    position_size: Optional[float] = None
    risk_reward_ratio: Optional[float] = None

@dataclass
class MarketAnalysis:
    symbol: str
    current_price: float
    trend: str
    volatility: float
    rsi: Optional[float]
    ma_20: Optional[float]
    ma_50: Optional[float]
    support_level: Optional[float]
    resistance_level: Optional[float]
    market_condition: MarketCondition

# Logging setup
log = logging.getLogger("investment_planner")
handler = logging.StreamHandler(sys.stdout)
handler.setFormatter(logging.Formatter("%(asctime)s | %(levelname)s | %(message)s"))
log.addHandler(handler)
log.setLevel(logging.INFO)

# HTTP session
session = requests.Session()

def get_json(url: str, params: dict | None = None, *, max_retries: int = 3, timeout: int = 12):
    """Enhanced HTTP client with retry logic and error handling"""
    for attempt in range(1, max_retries + 1):
        try:
            r = session.get(url, params=params, timeout=timeout)
            if r.status_code == 429:
                wait = min(2 ** attempt, 8)
                log.warning("429 from %s; backing off %ss", url, wait)
                time.sleep(wait)
                continue
            if r.status_code >= 500:
                wait = min(1.5 * attempt, 6)
                log.warning("%s %s; retrying in %.1fs", r.status_code, url, wait)
                time.sleep(wait)
                continue
            if r.status_code != 200:
                log.error("HTTP %s: %s", r.status_code, url)
                return None
            return r.json()
        except requests.RequestException as e:
            wait = min(1.5 * attempt, 6)
            log.warning("HTTP error (%s); retrying in %.1fs", e, wait)
            time.sleep(wait)
    log.error("Failed after retries: %s", url)
    return None

# Data fetching functions
def fmp_quote(symbol: str, apikey: str) -> Optional[dict]:
    """Fetch real-time quote data"""
    url = f"{FMP_BASE}/quote/{symbol}"
    js = get_json(url, {"apikey": apikey})
    if isinstance(js, list) and js:
        return js[0]
    return None

def fmp_profile(symbol: str, apikey: str) -> Optional[dict]:
    """Fetch company profile data"""
    url = f"{FMP_BASE}/profile/{symbol}"
    js = get_json(url, {"apikey": apikey})
    if isinstance(js, list) and js:
        return js[0]
    return None

def fmp_historical_daily(symbol: str, apikey: str, limit: int = 60) -> Optional[List[dict]]:
    """Fetch historical daily price data"""
    url = f"{FMP_BASE}/historical-price-full/{symbol}"
    js = get_json(url, {"timeseries": limit, "apikey": apikey})
    if js and isinstance(js, dict) and js.get("historical"):
        return js["historical"][::-1]  # oldest->newest
    return None

def fmp_options_chain(symbol: str, apikey: str) -> Optional[List[dict]]:
    """Fetch options chain data for strategy analysis"""
    url = f"{FMP_BASE}/options/{symbol}"
    js = get_json(url, {"apikey": apikey})
    if isinstance(js, list):
        return js
    return None

def fmp_earnings_next(symbol: str, apikey: str) -> Optional[dict]:
    """Fetch next earnings date"""
    today = datetime.now(timezone.utc).date()
    url = f"{FMP_BASE}/earning_calendar"
    js = get_json(url, {
        "symbol": symbol,
        "from": (today - timedelta(days=365)).isoformat(),
        "to": (today + timedelta(days=365)).isoformat(),
        "apikey": apikey,
    })
    if isinstance(js, list) and js:
        upcoming = sorted(js, key=lambda x: x.get("date") or "9999-99-99")
        for ev in upcoming:
            try:
                d = datetime.strptime(ev["date"], "%Y-%m-%d").date()
                if d >= today:
                    return ev
            except Exception:
                continue
        return upcoming[-1]
    return None

def fmp_news_today(symbols: List[str], apikey: str, limit: int = 30) -> List[dict]:
    """Fetch today's news for symbols"""
    url = f"{FMP_BASE}/stock_news"
    js = get_json(url, {"tickers": ",".join(symbols), "limit": limit, "apikey": apikey})
    if not isinstance(js, list):
        return []
    today_utc = datetime.now(timezone.utc).date()
    out: List[dict] = []
    for item in js:
        try:
            dt = datetime.fromisoformat(item.get("publishedDate").replace("Z", "+00:00"))
        except Exception:
            continue
        if dt.date() == today_utc:
            out.append({
                "symbol": (item.get("symbol") or "").upper(),
                "site": item.get("site"),
                "headline": item.get("title"),
                "time": dt,
                "url": item.get("url"),
            })
    return out

def resolve_fmp_key(cli_key: Optional[str]) -> Optional[str]:
    """Resolve FMP API key from various sources"""
    # 1) CLI
    if cli_key and cli_key.strip():
        return cli_key.strip()
    # 2) ENV
    key = os.getenv("FMP_API_KEY")
    if key and key.strip():
        return key.strip()
    # 3) .env (optional)
    try:
        from dotenv import load_dotenv
        load_dotenv()
        key = os.getenv("FMP_API_KEY")
        if key and key.strip():
            return key.strip()
    except Exception:
        pass
    # 4) fmp.key file
    try:
        if os.path.isfile("fmp.key"):
            with open("fmp.key", "r", encoding="utf-8") as fh:
                key = fh.read().strip()
                if key:
                    return key
    except Exception:
        pass
    # 5) Embedded fallback (last resort)
    if FMP_FALLBACK_KEY:
        return FMP_FALLBACK_KEY
    return None

# Technical Analysis and Volatility Calculations
def approx_atr14_from_daily(hist: List[dict]) -> Optional[float]:
    """Wilder's ATR(14) on daily candles"""
    if not hist or len(hist) < 15:
        return None
    trs = []
    prev_close = hist[0]["close"]
    for bar in hist[1:]:
        high, low, close = bar["high"], bar["low"], bar["close"]
        tr = max(high - low, abs(high - prev_close), abs(low - prev_close))
        trs.append(tr)
        prev_close = close
    n = 14
    if len(trs) < n:
        return None
    atr = sum(trs[:n]) / n
    for x in trs[n:]:
        atr = (atr * (n - 1) + x) / n
    return round(atr, 2)

# Formatting helpers
def pct(x: Optional[float]) -> str:
    if x is None or (isinstance(x, float) and math.isnan(x)):
        return "—"
    return f"{x:.2f}%"

def human_time(dt_utc: datetime, tz_name: str) -> str:
    tz_obj = tz.gettz(tz_name)
    return dt_utc.astimezone(tz_obj).strftime("%Y-%m-%d %H:%M")

def days_until(date_str: Optional[str]) -> str:
    if not date_str:
        return "—"
    try:
        d = datetime.strptime(date_str, "%Y-%m-%d").date()
        t = datetime.now(timezone.utc).date()
        delta = (d - t).days
        sign = "T" if delta >= 0 else "T-"
        return f"{sign}{abs(delta)}d"
    except Exception:
        return "—"

def pos_in_range(price: float, low: float, high: float) -> str:
    try:
        if high <= low:
            return "—"
        pctpos = 100.0 * (price - low) / (high - low)
        return f"{pctpos:.1f}% of 52W range"
    except Exception:
        return "—"

# Enhanced outline building with strategy integration
def build_enhanced_outline(symbol: str, apikey: str, tz_name: str,
                          shared_daily_hist: Dict[str, Optional[List[dict]]],
                          analysis_mode: str = "basic") -> str:
    """Build enhanced outline with optional strategy analysis"""

    q = fmp_quote(symbol, apikey)
    if not q:
        return f"# {symbol} ! No quote data available."

    price = q.get("price")
    chg = q.get("change")
    chgp = q.get("changesPercentage")
    dayLow, dayHigh = q.get("dayLow"), q.get("dayHigh")
    vol, avgVol = q.get("volume"), q.get("avgVolume")
    yearLow, yearHigh = q.get("yearLow"), q.get("yearHigh")

    # Relative volume
    rvol = None
    try:
        if vol and avgVol and float(avgVol) > 0:
            rvol = float(vol) / float(avgVol)
    except Exception:
        rvol = None

    # ATR(14)
    hist = shared_daily_hist.get(symbol)
    if hist is None:
        hist = fmp_historical_daily(symbol, apikey, limit=60)
        shared_daily_hist[symbol] = hist
    atr = approx_atr14_from_daily(hist) if hist else None

    # Earnings
    er = fmp_earnings_next(symbol, apikey)
    er_date = er.get("date") if isinstance(er, dict) else None
    er_time = er.get("time") if isinstance(er, dict) else None

    now_utc = datetime.now(timezone.utc)
    header = f"# {symbol} — {human_time(now_utc, tz_name)} {tz_name}\n"

    price_str = f"{price:.2f}" if isinstance(price, (int, float)) else str(price)
    chg_str = (f"{chg:+.2f}" if isinstance(chg, (int, float)) else str(chg) if chg is not None else "")
    chgp_str = f"{chgp:.2f}%" if isinstance(chgp, (int, float)) else (str(chgp) if chgp is not None else "—")

    line1 = f"  Price: {price_str} ({chgp_str} {chg_str})\n"

    range52 = pos_in_range(price, yearLow or 0, yearHigh or 0) if price and yearLow and yearHigh else "—"
    line2 = f"  Day: {dayLow} – {dayHigh} | 52W: {yearLow} – {yearHigh} | {range52}\n"

    rvol_str = f"{rvol:.2f}" if isinstance(rvol, float) else "—"
    atr_str = f"{atr}" if atr is not None else "—"
    line3 = f"  Vol: {vol} | Avg(30d): {avgVol} | RVOL: {rvol_str} | ATR14≈ {atr_str}\n"

    er_str = f"{er_date} {er_time or ''}".strip() if er_date else "—"
    line4 = f"  Next earnings: {er_str} ({days_until(er_date)})\n"

    basic_outline = header + line1 + line2 + line3 + line4

    # Add strategy analysis if in full mode
    if analysis_mode == "full":
        try:
            from daily_recommendations import DailyRecommendationGenerator

            # Create recommendation generator
            rec_generator = DailyRecommendationGenerator()

            # Prepare data for analysis
            market_data = {symbol: {"quote": q}}
            historical_data = {symbol: hist} if hist else {}

            if historical_data:
                # Generate recommendation for this symbol
                recommendation = rec_generator.strategy_engine.analyze_symbol(
                    symbol, market_data[symbol], historical_data[symbol]
                )

                # Add strategy analysis to outline
                strategy_lines = []
                strategy_lines.append(f"  Strategy Analysis (Confidence: {recommendation.confidence_score:.0%}):")

                if recommendation.primary_signal:
                    signal = recommendation.primary_signal
                    strategy_lines.append(f"    → {signal.action} {signal.strategy.value}")
                    strategy_lines.append(f"    → {signal.reasoning}")
                    if signal.target_price:
                        strategy_lines.append(f"    → Target: ${signal.target_price:.2f}")

                strategy_lines.append(f"  Market Outlook: {recommendation.market_outlook}")
                strategy_lines.append(f"  Risk Assessment: {recommendation.risk_assessment}")

                basic_outline += "\n".join(strategy_lines) + "\n"

        except ImportError:
            basic_outline += "  Strategy Analysis: Module not available\n"
        except Exception as e:
            basic_outline += f"  Strategy Analysis: Error - {str(e)}\n"

    return basic_outline

# Unit tests
def run_unit_tests() -> None:
    """Run offline unit tests"""
    # Test pct function
    assert pct(None) == "—"
    assert pct(1) == "1.00%"

    # Test pos_in_range function
    assert pos_in_range(150, 100, 200).startswith("50.0%")
    assert pos_in_range(100, 100, 200).startswith("0.0%")
    assert pos_in_range(200, 100, 200).startswith("100.0%")
    assert pos_in_range(10, 10, 10) == "—"

    # Test days_until function
    today = datetime.now(timezone.utc).strftime("%Y-%m-%d")
    assert days_until(today) == "T0d"

    print("[UNIT-TEST] All offline unit tests passed.")

def run_self_tests(apikey: str) -> None:
    """Minimal live smoke tests against FMP endpoints"""
    assert apikey, "FMP key required for tests"
    sym = "AAPL"
    q = fmp_quote(sym, apikey)
    assert q and q.get("symbol") == sym and "price" in q, "Quote smoke test failed"
    hist = fmp_historical_daily(sym, apikey, limit=60)
    assert hist and len(hist) >= 15, "Historical daily insufficient"
    assert approx_atr14_from_daily(hist) is not None, "ATR calc failed"
    er = fmp_earnings_next(sym, apikey)
    assert isinstance(er, dict) and "date" in er, "Earnings fetch failed"
    news = fmp_news_today([sym], apikey, limit=5)
    assert isinstance(news, list), "News fetch failed"
    print("[SELF-TEST] All live smoke tests passed.")

def main():
    parser = argparse.ArgumentParser(description="AI-Powered Daily Stock Investment Planning System")
    parser.add_argument("--symbols", default=",".join(SYMBOLS_DEFAULT))
    parser.add_argument("--tz", default="America/New_York")
    parser.add_argument("--news", type=int, default=3, help="# of headlines per symbol (today)")
    parser.add_argument("--fmp-key", dest="fmp_key", default=None, help="FMP API key")
    parser.add_argument("--analysis-mode", default="basic", choices=["basic", "full"],
                       help="Analysis depth: basic (market data) or full (with strategies)")
    parser.add_argument("--account-size", type=float, default=100000, help="Account size for position sizing")
    parser.add_argument("--self-test", action="store_true", help="Run live smoke tests and exit")
    parser.add_argument("--unit-test", action="store_true", help="Run offline unit tests and exit")
    args = parser.parse_args()

    if args.unit_test:
        run_unit_tests()
        return

    apikey = resolve_fmp_key(args.fmp_key)
    if not apikey:
        msg = (
            "Missing FMP API key. Provide one via --fmp-key, env FMP_API_KEY, .env, or a 'fmp.key' file.\n"
            "Example: python daily_outline.py --fmp-key YOUR_FMP_API_KEY\n"
        )
        log.error(msg)
        sys.exit(2)

    if args.self_test:
        run_self_tests(apikey)
        return

    symbols = [s.strip().upper() for s in args.symbols.split(",") if s.strip()]

    print("=" * 60)
    print("AI-POWERED DAILY STOCK INVESTMENT PLANNING SYSTEM")
    print("Enhanced with Erica's Trading Strategies (@AbundantlyErica)")
    print("=" * 60)
    print(f"Analysis Mode: {args.analysis_mode.upper()}")
    print(f"Account Size: ${args.account_size:,.0f}")
    print(f"Symbols: {', '.join(symbols)}")
    print("=" * 60)
    print()

    # Batch fetch news once and map to symbols
    news = fmp_news_today(symbols, apikey, limit=max(30, args.news * len(symbols)))
    news_by_symbol: Dict[str, List[dict]] = {s: [] for s in symbols}
    for n in news:
        if n["symbol"] in news_by_symbol:
            news_by_symbol[n["symbol"].upper()].append(n)

    shared_daily_hist: Dict[str, Optional[List[dict]]] = {}

    # Generate full daily report if in full analysis mode
    if args.analysis_mode == "full":
        try:
            from daily_recommendations import DailyRecommendationGenerator

            # Prepare data for comprehensive analysis
            market_data = {}
            historical_data = {}

            for symbol in symbols:
                quote = fmp_quote(symbol, apikey)
                if quote:
                    market_data[symbol] = {"quote": quote}

                hist = fmp_historical_daily(symbol, apikey, limit=60)
                if hist:
                    historical_data[symbol] = hist
                    shared_daily_hist[symbol] = hist

            # Generate comprehensive daily report
            rec_generator = DailyRecommendationGenerator(args.account_size, symbols)
            daily_report = rec_generator.generate_daily_report(market_data, historical_data)

            # Display formatted report
            print(rec_generator.format_daily_report(daily_report))

        except ImportError as e:
            print(f"Full analysis mode requires additional modules: {e}")
            print("Falling back to basic mode...")
            args.analysis_mode = "basic"
        except Exception as e:
            print(f"Error in full analysis mode: {e}")
            print("Falling back to basic mode...")
            args.analysis_mode = "basic"

    # Basic mode or fallback
    if args.analysis_mode == "basic":
        print("DAILY MARKET OUTLINE")
        print("=" * 25)

        for s in symbols:
            outline = build_enhanced_outline(s, apikey, args.tz, shared_daily_hist, args.analysis_mode)
            print(outline)

            # Add news if available
            items = news_by_symbol.get(s, [])[:args.news]
            if items:
                print("  News (today):")
                for it in items:
                    tloc = human_time(it["time"], args.tz).split(" ")[1]
                    headline = textwrap.shorten(it["headline"], width=96, placeholder="…")
                    print(f"   • {tloc} {it['site']}: {headline}")
            print()

        print("=" * 60)

if __name__ == "__main__":
    main()
