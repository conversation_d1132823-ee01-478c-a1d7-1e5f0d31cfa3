"""
Real-time Monitoring and Alert System
Continuous monitoring of market factors with intelligent alerts

This system provides:
- Real-time market factor monitoring
- Intelligent alert generation when conditions change
- Strategy invalidation detection
- Opportunity identification
- Risk level monitoring
- Performance tracking

Features:
- Configurable alert thresholds
- Multiple notification channels (console, email, SMS, desktop)
- Alert prioritization and filtering
- Historical alert tracking
- Performance analytics
- Automated strategy adjustments
"""

from typing import Dict, List, Optional, Tuple, Callable, Any
from dataclasses import dataclass, field
from datetime import datetime, timedelta
from enum import Enum
import threading
import time
import json
import logging

from market_analysis_engine import MarketFactors, StockSpecificFactors, MarketRegime, VolatilityRegime
from intelligent_strategy_engine import StrategyOfTheDay, MarketEnvironmentReport
from strategy_decision_tree import StrategyRecommendation

class AlertSeverity(Enum):
    LOW = "low"
    MEDIUM = "medium"
    HIGH = "high"
    CRITICAL = "critical"

class AlertType(Enum):
    MARKET_REGIME_CHANGE = "market_regime_change"
    VOLATILITY_SPIKE = "volatility_spike"
    STRATEGY_INVALIDATION = "strategy_invalidation"
    PROFIT_TARGET_HIT = "profit_target_hit"
    STOP_LOSS_TRIGGERED = "stop_loss_triggered"
    NEW_OPPORTUNITY = "new_opportunity"
    RISK_LEVEL_CHANGE = "risk_level_change"
    EARNINGS_ANNOUNCEMENT = "earnings_announcement"
    NEWS_IMPACT = "news_impact"
    TECHNICAL_BREAKOUT = "technical_breakout"

class NotificationChannel(Enum):
    CONSOLE = "console"
    EMAIL = "email"
    SMS = "sms"
    DESKTOP = "desktop"
    WEBHOOK = "webhook"
    LOG_FILE = "log_file"

@dataclass
class Alert:
    """Individual alert with all details"""
    id: str
    timestamp: datetime
    alert_type: AlertType
    severity: AlertSeverity
    symbol: Optional[str]
    title: str
    message: str
    data: Dict[str, Any] = field(default_factory=dict)
    acknowledged: bool = False
    resolved: bool = False
    actions_suggested: List[str] = field(default_factory=list)

@dataclass
class MonitoringThresholds:
    """Configurable thresholds for monitoring"""
    
    # VIX thresholds
    vix_spike_threshold: float = 5.0  # 5 point spike
    vix_extreme_level: float = 30.0
    
    # Market regime change
    regime_confidence_threshold: float = 0.8
    
    # Volatility changes
    iv_rank_change_threshold: float = 0.2  # 20 percentage point change
    
    # Price movements
    price_move_threshold: float = 0.05  # 5% move
    unusual_volume_threshold: float = 2.0  # 2x average volume
    
    # Strategy-specific
    profit_target_threshold: float = 0.5  # 50% of max profit
    stop_loss_threshold: float = 2.0  # 2x premium collected
    
    # News and events
    news_sentiment_change: float = 0.4  # 40% sentiment change
    earnings_proximity_days: int = 7

@dataclass
class MonitoringConfiguration:
    """Configuration for monitoring system"""
    
    # Monitoring intervals
    market_data_interval: int = 60  # seconds
    news_check_interval: int = 300  # 5 minutes
    strategy_review_interval: int = 900  # 15 minutes
    
    # Alert settings
    enabled_alert_types: List[AlertType] = field(default_factory=lambda: list(AlertType))
    notification_channels: List[NotificationChannel] = field(default_factory=lambda: [NotificationChannel.CONSOLE])
    alert_cooldown_minutes: int = 15  # Prevent spam
    
    # Thresholds
    thresholds: MonitoringThresholds = field(default_factory=MonitoringThresholds)
    
    # Performance tracking
    track_performance: bool = True
    performance_update_interval: int = 3600  # 1 hour

class RealTimeMonitoringSystem:
    """Real-time monitoring and alert system"""
    
    def __init__(self, config: MonitoringConfiguration = None):
        self.config = config or MonitoringConfiguration()
        self.is_running = False
        self.monitoring_thread = None
        
        # Data storage
        self.current_market_factors: Optional[MarketFactors] = None
        self.current_stock_factors: Dict[str, StockSpecificFactors] = {}
        self.active_strategies: Dict[str, StrategyOfTheDay] = {}
        self.alerts_history: List[Alert] = []
        self.last_alert_times: Dict[str, datetime] = {}
        
        # Callbacks
        self.alert_callbacks: List[Callable[[Alert], None]] = []
        
        # Logger
        self.logger = logging.getLogger(__name__)
        
    def start_monitoring(self, symbols: List[str], initial_strategies: List[StrategyOfTheDay] = None):
        """Start real-time monitoring"""
        
        if self.is_running:
            self.logger.warning("Monitoring already running")
            return
        
        self.symbols = symbols
        if initial_strategies:
            for strategy in initial_strategies:
                self.active_strategies[strategy.symbol] = strategy
        
        self.is_running = True
        self.monitoring_thread = threading.Thread(target=self._monitoring_loop, daemon=True)
        self.monitoring_thread.start()
        
        self.logger.info(f"Started monitoring for symbols: {', '.join(symbols)}")
        self._send_alert(Alert(
            id=self._generate_alert_id(),
            timestamp=datetime.now(),
            alert_type=AlertType.NEW_OPPORTUNITY,
            severity=AlertSeverity.LOW,
            symbol=None,
            title="Monitoring Started",
            message=f"Real-time monitoring active for {len(symbols)} symbols"
        ))
    
    def stop_monitoring(self):
        """Stop real-time monitoring"""
        
        self.is_running = False
        if self.monitoring_thread:
            self.monitoring_thread.join(timeout=5)
        
        self.logger.info("Monitoring stopped")
    
    def add_alert_callback(self, callback: Callable[[Alert], None]):
        """Add callback function for alert notifications"""
        self.alert_callbacks.append(callback)
    
    def update_strategy(self, symbol: str, strategy: StrategyOfTheDay):
        """Update active strategy for a symbol"""
        self.active_strategies[symbol] = strategy
        self.logger.info(f"Updated strategy for {symbol}: {strategy.recommended_strategy.value}")
    
    def get_active_alerts(self, severity: AlertSeverity = None) -> List[Alert]:
        """Get active (unresolved) alerts"""
        alerts = [alert for alert in self.alerts_history if not alert.resolved]
        
        if severity:
            alerts = [alert for alert in alerts if alert.severity == severity]
        
        return sorted(alerts, key=lambda a: a.timestamp, reverse=True)
    
    def acknowledge_alert(self, alert_id: str):
        """Acknowledge an alert"""
        for alert in self.alerts_history:
            if alert.id == alert_id:
                alert.acknowledged = True
                self.logger.info(f"Alert {alert_id} acknowledged")
                break
    
    def resolve_alert(self, alert_id: str):
        """Resolve an alert"""
        for alert in self.alerts_history:
            if alert.id == alert_id:
                alert.resolved = True
                self.logger.info(f"Alert {alert_id} resolved")
                break
    
    def _monitoring_loop(self):
        """Main monitoring loop"""
        
        last_market_check = datetime.min
        last_news_check = datetime.min
        last_strategy_review = datetime.min
        
        while self.is_running:
            try:
                current_time = datetime.now()
                
                # Market data monitoring
                if (current_time - last_market_check).seconds >= self.config.market_data_interval:
                    self._check_market_factors()
                    last_market_check = current_time
                
                # News monitoring
                if (current_time - last_news_check).seconds >= self.config.news_check_interval:
                    self._check_news_and_events()
                    last_news_check = current_time
                
                # Strategy review
                if (current_time - last_strategy_review).seconds >= self.config.strategy_review_interval:
                    self._review_active_strategies()
                    last_strategy_review = current_time
                
                # Sleep for a short interval
                time.sleep(10)
                
            except Exception as e:
                self.logger.error(f"Error in monitoring loop: {e}")
                time.sleep(30)  # Wait longer on error
    
    def _check_market_factors(self):
        """Check for significant market factor changes"""
        
        try:
            # In production, would fetch real market data
            # For now, simulate market factor updates
            new_market_factors = self._fetch_current_market_factors()
            
            if self.current_market_factors:
                self._compare_market_factors(self.current_market_factors, new_market_factors)
            
            self.current_market_factors = new_market_factors
            
            # Check stock-specific factors
            for symbol in self.symbols:
                new_stock_factors = self._fetch_current_stock_factors(symbol)
                
                if symbol in self.current_stock_factors:
                    self._compare_stock_factors(symbol, self.current_stock_factors[symbol], new_stock_factors)
                
                self.current_stock_factors[symbol] = new_stock_factors
                
        except Exception as e:
            self.logger.error(f"Error checking market factors: {e}")
    
    def _compare_market_factors(self, old_factors: MarketFactors, new_factors: MarketFactors):
        """Compare market factors and generate alerts"""
        
        # VIX spike detection
        vix_change = new_factors.vix_level - old_factors.vix_level
        if abs(vix_change) >= self.config.thresholds.vix_spike_threshold:
            severity = AlertSeverity.HIGH if abs(vix_change) > 10 else AlertSeverity.MEDIUM
            
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.VOLATILITY_SPIKE,
                severity=severity,
                symbol=None,
                title=f"VIX Spike: {vix_change:+.1f} points",
                message=f"VIX moved from {old_factors.vix_level:.1f} to {new_factors.vix_level:.1f}",
                data={"old_vix": old_factors.vix_level, "new_vix": new_factors.vix_level},
                actions_suggested=["Review position sizes", "Consider defensive strategies"]
            ))
        
        # Market regime change
        if old_factors.market_regime != new_factors.market_regime:
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.MARKET_REGIME_CHANGE,
                severity=AlertSeverity.HIGH,
                symbol=None,
                title=f"Market Regime Change: {old_factors.market_regime.value} → {new_factors.market_regime.value}",
                message=f"Market regime shifted from {old_factors.market_regime.value} to {new_factors.market_regime.value}",
                data={"old_regime": old_factors.market_regime.value, "new_regime": new_factors.market_regime.value},
                actions_suggested=["Review all active strategies", "Adjust position sizing", "Consider strategy changes"]
            ))
        
        # Extreme volatility
        if new_factors.vix_level > self.config.thresholds.vix_extreme_level:
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.VOLATILITY_SPIKE,
                severity=AlertSeverity.CRITICAL,
                symbol=None,
                title=f"Extreme Volatility: VIX {new_factors.vix_level:.1f}",
                message=f"VIX reached extreme level of {new_factors.vix_level:.1f}",
                data={"vix_level": new_factors.vix_level},
                actions_suggested=["Reduce position sizes", "Consider closing risky positions", "Focus on premium selling"]
            ))
    
    def _compare_stock_factors(self, symbol: str, old_factors: StockSpecificFactors, new_factors: StockSpecificFactors):
        """Compare stock-specific factors and generate alerts"""
        
        # IV rank change
        iv_change = new_factors.iv_rank - old_factors.iv_rank
        if abs(iv_change) >= self.config.thresholds.iv_rank_change_threshold:
            severity = AlertSeverity.MEDIUM if abs(iv_change) > 0.3 else AlertSeverity.LOW
            
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.NEW_OPPORTUNITY if iv_change > 0 else AlertType.RISK_LEVEL_CHANGE,
                severity=severity,
                symbol=symbol,
                title=f"{symbol} IV Rank Change: {iv_change:+.0%}",
                message=f"{symbol} IV rank moved from {old_factors.iv_rank:.0%} to {new_factors.iv_rank:.0%}",
                data={"old_iv_rank": old_factors.iv_rank, "new_iv_rank": new_factors.iv_rank},
                actions_suggested=["Review premium selling opportunities" if iv_change > 0 else "Consider closing premium positions"]
            ))
        
        # Earnings proximity
        if (new_factors.earnings_days_away and 
            new_factors.earnings_days_away <= self.config.thresholds.earnings_proximity_days and
            (not old_factors.earnings_days_away or old_factors.earnings_days_away > self.config.thresholds.earnings_proximity_days)):
            
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.EARNINGS_ANNOUNCEMENT,
                severity=AlertSeverity.MEDIUM,
                symbol=symbol,
                title=f"{symbol} Earnings in {new_factors.earnings_days_away} days",
                message=f"{symbol} earnings approaching - review positions for IV crush opportunity",
                data={"days_to_earnings": new_factors.earnings_days_away},
                actions_suggested=["Consider closing positions", "Evaluate IV crush opportunity", "Review earnings strategy"]
            ))
        
        # News sentiment change
        sentiment_change = new_factors.news_sentiment_score - old_factors.news_sentiment_score
        if abs(sentiment_change) >= self.config.thresholds.news_sentiment_change:
            severity = AlertSeverity.MEDIUM if abs(sentiment_change) > 0.6 else AlertSeverity.LOW
            
            self._send_alert(Alert(
                id=self._generate_alert_id(),
                timestamp=datetime.now(),
                alert_type=AlertType.NEWS_IMPACT,
                severity=severity,
                symbol=symbol,
                title=f"{symbol} News Sentiment Change: {sentiment_change:+.1f}",
                message=f"{symbol} news sentiment shifted significantly",
                data={"sentiment_change": sentiment_change},
                actions_suggested=["Review news impact", "Consider position adjustments"]
            ))
    
    def _check_news_and_events(self):
        """Check for news and events that might impact strategies"""
        
        try:
            # In production, would check news feeds and economic calendars
            # For now, simulate news checking
            
            for symbol in self.symbols:
                # Check for breaking news
                if self._has_breaking_news(symbol):
                    self._send_alert(Alert(
                        id=self._generate_alert_id(),
                        timestamp=datetime.now(),
                        alert_type=AlertType.NEWS_IMPACT,
                        severity=AlertSeverity.HIGH,
                        symbol=symbol,
                        title=f"{symbol} Breaking News",
                        message=f"Breaking news detected for {symbol} - review impact on positions",
                        actions_suggested=["Review news details", "Assess position impact", "Consider adjustments"]
                    ))
                
        except Exception as e:
            self.logger.error(f"Error checking news: {e}")
    
    def _review_active_strategies(self):
        """Review active strategies for profit targets, stop losses, etc."""
        
        try:
            for symbol, strategy in self.active_strategies.items():
                # Check profit targets (simulated)
                if self._check_profit_target_hit(symbol, strategy):
                    self._send_alert(Alert(
                        id=self._generate_alert_id(),
                        timestamp=datetime.now(),
                        alert_type=AlertType.PROFIT_TARGET_HIT,
                        severity=AlertSeverity.MEDIUM,
                        symbol=symbol,
                        title=f"{symbol} Profit Target Hit",
                        message=f"{symbol} {strategy.recommended_strategy.value} reached profit target",
                        actions_suggested=["Consider taking profits", "Review exit strategy"]
                    ))
                
                # Check stop loss levels (simulated)
                if self._check_stop_loss_triggered(symbol, strategy):
                    self._send_alert(Alert(
                        id=self._generate_alert_id(),
                        timestamp=datetime.now(),
                        alert_type=AlertType.STOP_LOSS_TRIGGERED,
                        severity=AlertSeverity.HIGH,
                        symbol=symbol,
                        title=f"{symbol} Stop Loss Triggered",
                        message=f"{symbol} {strategy.recommended_strategy.value} hit stop loss level",
                        actions_suggested=["Close position immediately", "Review risk management"]
                    ))
                
                # Check strategy invalidation
                if self._check_strategy_invalidation(symbol, strategy):
                    self._send_alert(Alert(
                        id=self._generate_alert_id(),
                        timestamp=datetime.now(),
                        alert_type=AlertType.STRATEGY_INVALIDATION,
                        severity=AlertSeverity.HIGH,
                        symbol=symbol,
                        title=f"{symbol} Strategy Invalidated",
                        message=f"{symbol} {strategy.recommended_strategy.value} thesis no longer valid",
                        actions_suggested=["Close position", "Reassess strategy", "Consider alternatives"]
                    ))
                
        except Exception as e:
            self.logger.error(f"Error reviewing strategies: {e}")
    
    def _send_alert(self, alert: Alert):
        """Send alert through configured channels"""
        
        # Check cooldown to prevent spam
        alert_key = f"{alert.alert_type.value}_{alert.symbol or 'market'}"
        if alert_key in self.last_alert_times:
            time_since_last = datetime.now() - self.last_alert_times[alert_key]
            if time_since_last.seconds < self.config.alert_cooldown_minutes * 60:
                return  # Skip this alert due to cooldown
        
        self.last_alert_times[alert_key] = datetime.now()
        
        # Store alert
        self.alerts_history.append(alert)
        
        # Send through configured channels
        for channel in self.config.notification_channels:
            try:
                if channel == NotificationChannel.CONSOLE:
                    self._send_console_alert(alert)
                elif channel == NotificationChannel.LOG_FILE:
                    self._send_log_alert(alert)
                # Add other notification channels as needed
                
            except Exception as e:
                self.logger.error(f"Error sending alert via {channel.value}: {e}")
        
        # Call registered callbacks
        for callback in self.alert_callbacks:
            try:
                callback(alert)
            except Exception as e:
                self.logger.error(f"Error in alert callback: {e}")
    
    def _send_console_alert(self, alert: Alert):
        """Send alert to console"""
        
        severity_colors = {
            AlertSeverity.LOW: "\033[92m",      # Green
            AlertSeverity.MEDIUM: "\033[93m",   # Yellow
            AlertSeverity.HIGH: "\033[91m",     # Red
            AlertSeverity.CRITICAL: "\033[95m"  # Magenta
        }
        
        color = severity_colors.get(alert.severity, "")
        reset = "\033[0m"
        
        symbol_text = f"[{alert.symbol}] " if alert.symbol else "[MARKET] "
        
        print(f"\n{color}🚨 ALERT {symbol_text}{alert.title}{reset}")
        print(f"   {alert.message}")
        print(f"   Severity: {alert.severity.value.upper()}")
        print(f"   Time: {alert.timestamp.strftime('%H:%M:%S')}")
        
        if alert.actions_suggested:
            print("   Suggested Actions:")
            for action in alert.actions_suggested:
                print(f"   • {action}")
        print()
    
    def _send_log_alert(self, alert: Alert):
        """Send alert to log file"""
        
        log_level = {
            AlertSeverity.LOW: logging.INFO,
            AlertSeverity.MEDIUM: logging.WARNING,
            AlertSeverity.HIGH: logging.ERROR,
            AlertSeverity.CRITICAL: logging.CRITICAL
        }.get(alert.severity, logging.INFO)
        
        self.logger.log(log_level, f"ALERT [{alert.symbol or 'MARKET'}] {alert.title}: {alert.message}")
    
    def _fetch_current_market_factors(self) -> MarketFactors:
        """Fetch current market factors (simulated)"""
        
        # In production, would fetch real data
        # For now, return simulated data with some variation
        import random
        
        base_vix = 20.0
        vix_variation = random.uniform(-2, 2)
        
        from market_analysis_engine import MarketFactors, MarketRegime, VolatilityRegime, SentimentLevel
        
        return MarketFactors(
            vix_level=base_vix + vix_variation,
            vix_percentile=random.uniform(30, 70),
            put_call_ratio=random.uniform(0.8, 1.2),
            market_breadth=random.uniform(0.9, 1.8),
            volatility_regime=VolatilityRegime.NORMAL_VOL,
            iv_rank_spy=random.uniform(0.3, 0.7),
            iv_term_structure="normal",
            market_regime=MarketRegime.BULL_MARKET,
            trend_strength=random.uniform(0.4, 0.8),
            regime_confidence=random.uniform(0.6, 0.9),
            fear_greed_index=random.uniform(50, 75),
            sentiment_level=SentimentLevel.GREED,
            social_sentiment=random.uniform(0.1, 0.5),
            spy_technical_score=random.uniform(0.5, 0.8),
            sector_rotation_signal="growth_rotation",
            relative_strength_leaders=["Technology"],
            earnings_season_intensity=random.uniform(0.3, 0.7),
            fed_meeting_proximity=15,
            major_events_this_week=[],
            bullish_factors=random.uniform(0.6, 0.8),
            bearish_factors=random.uniform(0.2, 0.4),
            volatility_factors=random.uniform(0.4, 0.7),
            uncertainty_factors=random.uniform(0.2, 0.5)
        )
    
    def _fetch_current_stock_factors(self, symbol: str) -> StockSpecificFactors:
        """Fetch current stock factors (simulated)"""
        
        import random
        
        return StockSpecificFactors(
            symbol=symbol,
            news_sentiment_score=random.uniform(-0.3, 0.5),
            earnings_days_away=random.choice([None, 5, 12, 25, 45]),
            earnings_move_estimate=random.uniform(0.06, 0.12),
            recent_analyst_changes=[],
            relative_strength_vs_spy=random.uniform(-0.1, 0.4),
            technical_confluence_score=random.uniform(0.4, 0.8),
            support_resistance_clarity=random.uniform(0.5, 0.9),
            trend_alignment="up",
            unusual_options_activity=random.choice([True, False]),
            iv_rank=random.uniform(0.3, 0.8),
            iv_vs_hv_ratio=random.uniform(0.9, 1.3),
            options_flow_sentiment="neutral",
            sector_performance=random.uniform(0.0, 0.3),
            sector_rotation_impact="positive",
            wheel_suitability_score=random.uniform(0.5, 0.9),
            covered_call_attractiveness=random.uniform(0.6, 0.9),
            credit_spread_opportunity=random.uniform(0.5, 0.8),
            leaps_opportunity=random.uniform(0.4, 0.7)
        )
    
    def _has_breaking_news(self, symbol: str) -> bool:
        """Check for breaking news (simulated)"""
        import random
        return random.random() < 0.01  # 1% chance of breaking news
    
    def _check_profit_target_hit(self, symbol: str, strategy: StrategyOfTheDay) -> bool:
        """Check if profit target was hit (simulated)"""
        import random
        return random.random() < 0.05  # 5% chance
    
    def _check_stop_loss_triggered(self, symbol: str, strategy: StrategyOfTheDay) -> bool:
        """Check if stop loss was triggered (simulated)"""
        import random
        return random.random() < 0.02  # 2% chance
    
    def _check_strategy_invalidation(self, symbol: str, strategy: StrategyOfTheDay) -> bool:
        """Check if strategy thesis is invalidated (simulated)"""
        import random
        return random.random() < 0.03  # 3% chance
    
    def _generate_alert_id(self) -> str:
        """Generate unique alert ID"""
        import uuid
        return str(uuid.uuid4())[:8]
